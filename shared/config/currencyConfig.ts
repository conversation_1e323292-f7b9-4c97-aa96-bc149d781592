// shared/config/currencyConfig.ts
// Centralized currency configuration system
// Change currency settings in ONE place and it reflects everywhere!

import { LKRCurrencyVariant } from '@/shared/UI/components/icons/LKRCurrencyIcon';

/**
 * Currency Configuration Interface
 * Defines all the properties needed for a currency
 */
export interface CurrencyConfig {
  /** ISO currency code (e.g., 'LKR', 'USD', 'EUR') */
  code: string;
  /** Display name of the currency */
  name: string;
  /** Text symbol for the currency (e.g., 'Rs', '$', '€') */
  symbol: string;
  /** Icon variant for LKR currency icons (for non-LKR currencies, this determines fallback styling) */
  iconVariant: LKRCurrencyVariant;
  /** Whether this currency uses custom icons (LKR) or text symbols */
  hasCustomIcon: boolean;
  /** Decimal places to show */
  decimalPlaces: number;
  /** Locale for number formatting */
  locale: string;
  /** Position of symbol/icon relative to amount */
  symbolPosition: 'before' | 'after';
  /** Thousands separator */
  thousandsSeparator: ',' | '.' | ' ';
  /** Decimal separator */
  decimalSeparator: '.' | ',';
}

/**
 * Available Currency Configurations
 * Add new currencies here as needed
 */
export const AVAILABLE_CURRENCIES: Record<string, CurrencyConfig> = {
  LKR: {
    code: 'LKR',
    name: 'Sri Lankan Rupee',
    symbol: 'Rs',
    iconVariant: 'black', // Default variant, can be overridden per context
    hasCustomIcon: true,
    decimalPlaces: 2,
    locale: 'en-US',
    symbolPosition: 'before',
    thousandsSeparator: ',',
    decimalSeparator: '.'
  },
  USD: {
    code: 'USD',
    name: 'US Dollar',
    symbol: '$',
    iconVariant: 'black',
    hasCustomIcon: false,
    decimalPlaces: 2,
    locale: 'en-US',
    symbolPosition: 'before',
    thousandsSeparator: ',',
    decimalSeparator: '.'
  },
  EUR: {
    code: 'EUR',
    name: 'Euro',
    symbol: '€',
    iconVariant: 'black',
    hasCustomIcon: false,
    decimalPlaces: 2,
    locale: 'en-US',
    symbolPosition: 'before',
    thousandsSeparator: ',',
    decimalSeparator: '.'
  },
  GBP: {
    code: 'GBP',
    name: 'British Pound',
    symbol: '£',
    iconVariant: 'black',
    hasCustomIcon: false,
    decimalPlaces: 2,
    locale: 'en-GB',
    symbolPosition: 'before',
    thousandsSeparator: ',',
    decimalSeparator: '.'
  },
  INR: {
    code: 'INR',
    name: 'Indian Rupee',
    symbol: '₹',
    iconVariant: 'black',
    hasCustomIcon: false,
    decimalPlaces: 2,
    locale: 'en-IN',
    symbolPosition: 'before',
    thousandsSeparator: ',',
    decimalSeparator: '.'
  },
  CHIPS: {
    code: 'chips',
    name: 'Chips',
    symbol: 'C',
    iconVariant: 'black',
    hasCustomIcon: false,
    decimalPlaces: 2,
    locale: 'en-US',
    symbolPosition: 'before',
    thousandsSeparator: ',',
    decimalSeparator: '.'
  }
};

/**
 * 🎯 MAIN CURRENCY SETTING - CHANGE HERE TO SWITCH ENTIRE APP CURRENCY!
 * 
 * To change the application currency:
 * 1. Change this value to any key from AVAILABLE_CURRENCIES
 * 2. The entire application will automatically use the new currency
 * 
 * Examples:
 * - 'LKR' for Sri Lankan Rupee (with custom icons)
 * - 'USD' for US Dollar
 * - 'EUR' for Euro
 * - 'GBP' for British Pound
 * - 'INR' for Indian Rupee
 */
export const DEFAULT_CURRENCY_CODE: keyof typeof AVAILABLE_CURRENCIES = 'CHIPS';

/**
 * Get the current currency configuration
 * This is the main function components should use
 */
export const getCurrentCurrencyConfig = (): CurrencyConfig => {
  return AVAILABLE_CURRENCIES[DEFAULT_CURRENCY_CODE];
};

/**
 * Get currency configuration by code
 * Useful for handling multiple currencies or user preferences
 */
export const getCurrencyConfig = (currencyCode: string): CurrencyConfig => {
  return AVAILABLE_CURRENCIES[currencyCode] || getCurrentCurrencyConfig();
};

/**
 * Context-specific icon variants
 * These override the default iconVariant based on UI context
 */
export const CONTEXT_ICON_VARIANTS: Record<string, LKRCurrencyVariant> = {
  'header': 'white',           // Header components (dark background)
  'card': 'white',             // Summary cards (dark background)
  'modal': 'white',            // Modal components (dark background)
  'table': 'black',            // Table cells (light background)
  'form': 'black',             // Form inputs (light background)
  'default': 'black'           // Default fallback
};

/**
 * Get icon variant for specific context
 */
export const getIconVariantForContext = (context: keyof typeof CONTEXT_ICON_VARIANTS = 'default'): LKRCurrencyVariant => {
  return CONTEXT_ICON_VARIANTS[context] || CONTEXT_ICON_VARIANTS.default;
};

/**
 * Utility to check if current currency has custom icons
 */
export const currentCurrencyHasCustomIcon = (): boolean => {
  return getCurrentCurrencyConfig().hasCustomIcon;
};

/**
 * Legacy currency ID mappings for backward compatibility
 * Maps old currency IDs to new currency codes
 */
export const LEGACY_CURRENCY_ID_MAP: Record<string, string> = {
  '1': 'LKR', // Legacy ID 1 was LKR
  '2': 'EUR',
  '3': 'GBP',
  '4': 'USD',
  '5': 'USD',
  '6': 'USD',
  '7': 'USD',
  '8': 'USD',
  '9': 'INR',
  '10': 'CHIPS', // Current API uses ID 10 for Chips
  // Add more mappings as needed
};

/**
 * Convert legacy currency ID to current currency code
 */
export const convertLegacyCurrencyId = (currencyId: string | number): string => {
  const id = currencyId.toString();
  return LEGACY_CURRENCY_ID_MAP[id] || DEFAULT_CURRENCY_CODE;
};
