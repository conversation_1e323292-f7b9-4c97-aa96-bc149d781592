// shared/hooks/business/useOptimizedGlobalFinancialReport.ts
"use client";

import { useAuthStore } from "@/shared/stores/authStore";
import { DEFAULT_FINANCIAL_REPORT_FILTERS, FinancialReportFilters, FinancialReportResponse } from "@/shared/types/report-types";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import {
  useFinancialReportList,
  useFinancialReportCount,
  useFinancialReportSummary
} from "@/shared/query/useOptimizedFinancialReportQuery";

interface UseOptimizedGlobalFinancialReportOptions {
  initialFilters?: Partial<FinancialReportFilters>;
  initialFinancialReportResponse?: FinancialReportResponse | null;
}

interface UseOptimizedGlobalFinancialReportReturn {
  // State
  filters: FinancialReportFilters;

  // Data from 3 separate API calls
  listData: FinancialReportResponse | null; // API 1: Financial report list (dateRange filter)
  totalCount: number; // API 2: Pagination count
  summaryData: FinancialReportResponse | null; // API 3: Summary cards (dateRange filter with totals)

  // Loading states
  isListLoading: boolean;
  isCountLoading: boolean;
  isSummaryLoading: boolean;
  isAnyLoading: boolean;
  isFetching: boolean;

  // Error states
  isError: boolean;
  error: any;

  // Computed values for summary cards (from summaryData)
  totalTransactions: number;
  totalAmount: number;
  totalDeposits: number;
  totalWithdrawals: number;

  // Actions
  handleFilterChange: (newFilters: Partial<FinancialReportFilters>) => void;
  handlePageChange: (page: number) => void;
  handleRefresh: () => void;

  // Authentication state
  isAuthenticated: boolean;
  hasHydrated: boolean;
}

/**
 * Optimized hook for global financial reports with exactly 3 API calls:
 * 1. Financial report list: dateRange filter without totals
 * 2. Pagination count: same filters as list with count=true
 * 3. Summary cards: dateRange filter with totals=true
 * 
 * Note: No overview cards API call since this is global (no user created date)
 */
export const useOptimizedGlobalFinancialReport = (
  options: UseOptimizedGlobalFinancialReportOptions = {}
): UseOptimizedGlobalFinancialReportReturn => {
  const { initialFilters, initialFinancialReportResponse } = options;
  const router = useRouter();
  const { isAuthenticated, _hasHydrated } = useAuthStore();

  // State for filters (no playerId for global reports)
  const [filters, setFilters] = useState<FinancialReportFilters>(() => ({
    ...DEFAULT_FINANCIAL_REPORT_FILTERS,
    ...initialFilters,
    playerId: undefined // Ensure no playerId for global reports
  }));

  // API Call 1: Financial report list (dateRange filter without totals)
  const {
    data: listData,
    isLoading: isListLoading,
    error: listError,
    refetch: refetchList,
    isFetching: isListFetching
  } = useFinancialReportList(filters);

  // API Call 2: Pagination count (same filters as list with count=true)
  const {
    data: totalCount,
    isLoading: isCountLoading,
    error: countError,
    refetch: refetchCount,
    isFetching: isCountFetching
  } = useFinancialReportCount(filters);

  // API Call 3: Summary cards (dateRange filter with totals=true)
  const {
    data: summaryData,
    isLoading: isSummaryLoading,
    error: summaryError,
    refetch: refetchSummary,
    isFetching: isSummaryFetching
  } = useFinancialReportSummary(filters);

  // Redirect if not authenticated
  useEffect(() => {
    if (_hasHydrated && !isAuthenticated) {
      router.replace("/authentication/sign-in/");
    }
  }, [_hasHydrated, isAuthenticated, router]);

  // Handle filter changes
  const handleFilterChange = useCallback((newFilters: Partial<FinancialReportFilters>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
      playerId: undefined // Always ensure no playerId for global reports
    }));
  }, []);

  // Handle page changes
  const handlePageChange = useCallback((page: number) => {
    setFilters(prev => ({
      ...prev,
      page
    }));
  }, []);

  // Handle refresh - refetch all APIs
  const handleRefresh = useCallback(() => {
    refetchList();
    refetchCount();
    refetchSummary();
  }, [refetchList, refetchCount, refetchSummary]);

  // Computed values
  const isAnyLoading = isListLoading || isCountLoading || isSummaryLoading;
  const isFetching = isListFetching || isCountFetching || isSummaryFetching;
  const isError = !!(listError || countError || summaryError);
  const error = listError || countError || summaryError;

  // Summary card values from summaryData (API 3)
  const totalTransactions = totalCount || 0;
  const totalAmount = summaryData?.totalAmount || 0;
  const totalDeposits = summaryData?.totals?.totalDeposit || 0;
  const totalWithdrawals = summaryData?.totals?.totalWithdraw || 0;

  return {
    // State
    filters,

    // Data from 3 separate API calls
    listData: listData || null,
    totalCount: totalCount || 0,
    summaryData: summaryData || null,

    // Loading states
    isListLoading,
    isCountLoading,
    isSummaryLoading,
    isAnyLoading,
    isFetching,

    // Error states
    isError,
    error,

    // Computed values for summary cards
    totalTransactions,
    totalAmount,
    totalDeposits,
    totalWithdrawals,

    // Actions
    handleFilterChange,
    handlePageChange,
    handleRefresh,

    // Authentication state
    isAuthenticated,
    hasHydrated: _hasHydrated
  };
};
