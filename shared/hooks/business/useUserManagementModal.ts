// shared/hooks/business/useUserManagementModal.ts
"use client";

import { useState, useCallback, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useQueryClient } from "@tanstack/react-query";
import { UserDetailsData } from "@/shared/types/user-management-types";
import { UserManagementModalMode } from "@/shared/UI/components";
import { useCreateUserMutation, useEditUserMutation } from "@/shared/query";
import { CreateUserData, EditUserData } from "@/shared/query";
import { useToast } from "@/shared/UI/components";

interface UseUserManagementModalProps {
  onSuccess?: () => void;
}

interface UseUserManagementModalReturn {
  // Modal state
  isModalOpen: boolean;
  modalMode: UserManagementModalMode;
  selectedUser: UserDetailsData | undefined;

  // Modal actions
  openCreateModal: () => void;
  openEditModal: (userData: UserDetailsData) => void;
  openDeactivateModal: (userData: UserDetailsData) => void;
  openActivateModal: (userData: UserDetailsData) => void;
  closeModal: () => void;

  // Form actions
  handleCreateUser: (formData: CreateUserData) => Promise<void>;
  handleEditUser: (formData: EditUserData) => Promise<void>;
  handleDeactivateUser: (userId: number) => Promise<void>;
  handleActivateUser: (userId: number) => Promise<void>;

  // Loading states
  isCreating: boolean;
  isEditing: boolean;
  isDeactivating: boolean;
  isActivating: boolean;
}

/**
 * Custom hook for managing user management modal state and operations
 * 
 * This hook encapsulates all the business logic for the UserManagementModal component,
 * including modal state management, form submissions, and API calls.
 * 
 * Features:
 * - Modal state management (open/close, mode switching)
 * - Integration with existing user management mutations
 * - Loading state management
 * - Success/error handling
 * - Query cache invalidation
 */
export const useUserManagementModal = ({
  onSuccess
}: UseUserManagementModalProps = {}): UseUserManagementModalReturn => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useToast();

  // Modal state controlled by URL parameters
  const [selectedUser, setSelectedUser] = useState<UserDetailsData | undefined>();

  // Read modal state from URL parameters
  const modalParam = searchParams.get('modal');
  const userIdParam = searchParams.get('userId');
  const isModalOpen = modalParam === 'user-management';
  const modalMode = (searchParams.get('mode') as UserManagementModalMode) || 'create';

  // Mutations
  const createUserMutation = useCreateUserMutation();
  const editUserMutation = useEditUserMutation();

  // Helper function to update URL parameters
  const updateModalParams = useCallback((params: Record<string, string | null>) => {
    const current = new URLSearchParams(Array.from(searchParams.entries()));

    Object.entries(params).forEach(([key, value]) => {
      if (value === null) {
        current.delete(key);
      } else {
        current.set(key, value);
      }
    });

    const search = current.toString();
    const query = search ? `?${search}` : '';
    router.push(`${window.location.pathname}${query}`, { scroll: false });
  }, [router, searchParams]);

  // Modal actions
  const openCreateModal = useCallback(() => {
    setSelectedUser(undefined);
    updateModalParams({
      modal: 'user-management',
      mode: 'create',
      userId: null
    });
  }, [updateModalParams]);

  const openEditModal = useCallback((userData: UserDetailsData) => {
    setSelectedUser(userData);
    updateModalParams({
      modal: 'user-management',
      mode: 'edit',
      userId: userData.id.toString()
    });
  }, [updateModalParams]);

  const openDeactivateModal = useCallback((userData: UserDetailsData) => {
    setSelectedUser(userData);
    updateModalParams({
      modal: 'user-management',
      mode: 'deactivate',
      userId: userData.id.toString()
    });
  }, [updateModalParams]);

  const openActivateModal = useCallback((userData: UserDetailsData) => {
    setSelectedUser(userData);
    updateModalParams({
      modal: 'user-management',
      mode: 'activate',
      userId: userData.id.toString()
    });
  }, [updateModalParams]);

  const closeModal = useCallback(() => {
    setSelectedUser(undefined);
    updateModalParams({
      modal: null,
      mode: null,
      userId: null
    });
  }, [updateModalParams]);

  // Effect to handle user data loading when modal opens with userId
  useEffect(() => {
    if (isModalOpen && userIdParam && (modalMode === 'edit' || modalMode === 'deactivate')) {
      // You can add user data fetching logic here if needed
      // For now, we'll rely on the parent component to pass userData
    }
  }, [isModalOpen, userIdParam, modalMode]);

  // Form submission handlers
  const handleCreateUser = useCallback(async (formData: CreateUserData) => {
    try {
      const response = await createUserMutation.mutateAsync(formData);

      // Show success message
      if (response.success === 1) {
        showSuccess('', `User "${formData.firstName} ${formData.lastName}" has been successfully created.`);
      }

      // Invalidate user list queries to refresh the data
      queryClient.invalidateQueries({ queryKey: ['userList'] });

      // Call success callback
      onSuccess?.();

      // Close modal
      closeModal();
    } catch {
      // Show error message
      showError('', 'Failed to create user. Please try again.');
      // Don't close modal on error so user can retry
    }
  }, [createUserMutation, queryClient, onSuccess, closeModal, showSuccess, showError]);

  const handleEditUser = useCallback(async (formData: EditUserData) => {
    try {
      const response = await editUserMutation.mutateAsync(formData);

      // Show success message
      if (response.success === 1) {
        showSuccess('',
          `User "${formData.firstName} ${formData.lastName}" has been successfully updated.`
        );
      }

      // Invalidate user list queries to refresh the data
      queryClient.invalidateQueries({ queryKey: ['userList'] });

      // Invalidate specific user details query if we have the user ID
      if (selectedUser?.id) {
        queryClient.invalidateQueries({
          queryKey: ['userDetails', selectedUser.id.toString()]
        });
      }

      // Call success callback
      onSuccess?.();

      // Close modal
      closeModal();
    } catch {
      // Show error message
      showError(
        '',
        'Failed to update user. Please try again.'
      );
      // Don't close modal on error so user can retry
    }
  }, [editUserMutation, queryClient, selectedUser, onSuccess, closeModal, showSuccess, showError]);

  const handleDeactivateUser = useCallback(async (userId: number) => {
    try {
      // Only send the required fields for deactivate operation
      const updatePayload: Partial<EditUserData> & { id: number } = {
        id: userId,
        active: false // Deactivate the user
      };

      const response = await editUserMutation.mutateAsync(updatePayload as EditUserData);

      // Show success message
      if (response.success === 1) {
        showSuccess(
          '',
          `User has been successfully deactivated.`
        );
      }

      // Invalidate user list queries to refresh the data
      queryClient.invalidateQueries({ queryKey: ['userList'] });

      // Invalidate specific user details query
      queryClient.invalidateQueries({
        queryKey: ['userDetails', userId.toString()]
      });

      // Call success callback
      onSuccess?.();

      // Close modal
      closeModal();
    } catch {
      // Show error message
      showError(
        '',
        'Failed to deactivate user. Please try again.'
      );
      // Don't close modal on error so user can retry
    }
  }, [editUserMutation, queryClient, onSuccess, closeModal, showSuccess, showError]);

  const handleActivateUser = useCallback(async (userId: number) => {
    try {
      // Only send the required fields for activate operation
      const updatePayload: Partial<EditUserData> & { id: number } = {
        id: userId,
        active: true // Activate the user
      };

      const response = await editUserMutation.mutateAsync(updatePayload as EditUserData);

      // Show success message
      if (response.success === 1) {
        showSuccess(
          '',
          `User has been successfully activated.`
        );
      }

      // Invalidate user list queries to refresh the data
      queryClient.invalidateQueries({ queryKey: ['userList'] });

      // Invalidate specific user details query
      queryClient.invalidateQueries({
        queryKey: ['userDetails', userId.toString()]
      });

      // Call success callback
      onSuccess?.();

      // Close modal
      closeModal();
    } catch {
      // Show error message
      showError(
        '',
        'Failed to activate user. Please try again.'
      );
      // Don't close modal on error so user can retry
    }
  }, [editUserMutation, queryClient, onSuccess, closeModal, showSuccess, showError]);

  return {
    // Modal state
    isModalOpen,
    modalMode,
    selectedUser,

    // Modal actions
    openCreateModal,
    openEditModal,
    openDeactivateModal,
    openActivateModal,
    closeModal,

    // Form actions
    handleCreateUser,
    handleEditUser,
    handleDeactivateUser,
    handleActivateUser,

    // Loading states
    isCreating: createUserMutation.isPending,
    isEditing: editUserMutation.isPending,
    isDeactivating: editUserMutation.isPending,
    isActivating: editUserMutation.isPending
  };
};
